import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  On<PERSON><PERSON>roy,
  OnInit,
  signal
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';

// PrimeNG Components
import { AutoCompleteModule } from 'primeng/autocomplete';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';

import { TIPOS_EQUIPAMENTO } from '@core/models/equipamento.model';
import { MapaFiltros } from '@core/models/filtros.model';
import { EquipamentoService } from '@core/services/equipamento.service';
import { MapaFiltrosService } from '@core/services/mapa-filtros.service';

@Component({
  selector: 'app-painel-filtros',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    MultiSelectModule,
    AutoCompleteModule,
    CalendarModule,
    InputTextModule,
    DividerModule,
    BadgeModule
  ],
  templateUrl: './painel-filtros.component.html',
  styleUrl: './painel-filtros.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PainelFiltrosComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private filtrosService = inject(MapaFiltrosService);
  private equipamentoService = inject(EquipamentoService);

  // Signals para reatividade
  filtros = signal<MapaFiltros>(this.filtrosService.getFiltros());
  hasActiveFilters = computed(() => this.filtrosService.hasActiveFilters());

  // Opções para os selects
  tiposEquipamento = TIPOS_EQUIPAMENTO.map(tipo => ({
    label: tipo.descricao,
    value: tipo.codigo,
    icon: tipo.icone
  }));

  // Listas para autocomplete
  servicosSuggestions = signal<string[]>([]);
  clientesSuggestions = signal<string[]>([]);
  bairrosSuggestions = signal<string[]>([]);
  solicitantesSuggestions = signal<string[]>([]);

  ngOnInit(): void {
    // Observa mudanças nos filtros
    this.filtrosService.filtros$
      .pipe(takeUntil(this.destroy$))
      .subscribe(filtros => {
        this.filtros.set(filtros);
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Atualiza os tipos de equipamento selecionados
   */
  onTiposEquipamentoChange(tipos: string[]): void {
    // Garante que tipos seja sempre um array, mesmo se for null ou undefined
    const tiposValidos = tipos || [];
    this.filtrosService.updateFiltros({ tiposEquipamento: tiposValidos });
  }

  /**
   * Busca sugestões de serviços
   */
  searchServicos(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllServices(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(servicos => {
        this.servicosSuggestions.set(servicos);
      });
  }

  /**
   * Atualiza os serviços selecionados
   */
  onServicosChange(servicos: string[]): void {
    this.filtrosService.updateFiltros({ servicos });
  }

  /**
   * Busca sugestões de clientes
   */
  searchClientes(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllClients(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(clientes => {
        this.clientesSuggestions.set(clientes);
      });
  }

  /**
   * Atualiza os clientes selecionados
   */
  onClientesChange(clientes: string[]): void {
    this.filtrosService.updateFiltros({ clientes });
  }

  /**
   * Busca sugestões de bairros
   */
  searchBairros(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllPlaces(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(bairros => {
        this.bairrosSuggestions.set(bairros);
      });
  }

  /**
   * Atualiza os bairros selecionados
   */
  onBairrosChange(bairros: string[]): void {
    this.filtrosService.updateFiltros({ bairros });
  }

  /**
   * Busca sugestões de solicitantes
   */
  searchSolicitantes(event: any): void {
    const query = event.query;
    this.equipamentoService.findAllRequesters(query)
      .pipe(takeUntil(this.destroy$))
      .subscribe(solicitantes => {
        this.solicitantesSuggestions.set(solicitantes);
      });
  }

  /**
   * Atualiza os solicitantes selecionados
   */
  onSolicitantesChange(solicitantes: string[]): void {
    this.filtrosService.updateFiltros({ solicitantes });
  }

  /**
   * Atualiza a data de início
   */
  onDataInicioChange(data: Date): void {
    this.filtrosService.updateFiltros({ dataInicio: data });
  }

  /**
   * Atualiza a data de fim
   */
  onDataFimChange(data: Date): void {
    this.filtrosService.updateFiltros({ dataFim: data });
  }

  /**
   * Limpa todos os filtros
   */
  limparFiltros(): void {
    this.filtrosService.resetFiltros();
  }

  /**
   * Aplica os filtros (pode ser usado para trigger de busca)
   */
  aplicarFiltros(): void {
    // A aplicação dos filtros é automática através do service
    // Este método pode ser usado para feedback visual ou outras ações
  }
}
