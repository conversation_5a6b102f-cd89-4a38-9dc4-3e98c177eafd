.mapa-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.mapa-header {
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mapa-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.filtros-panel {
  transition: all 0.3s ease-in-out;
  transform: translateX(-100%);
  opacity: 0;
  width: 0;
  overflow: hidden;

  &.visible {
    transform: translateX(0);
    opacity: 1;
    width: auto;
  }
}

.mapa-area {
  position: relative;
  background-color: #e9ecef;
}

// Overlays
.loading-overlay,
.error-overlay,
.no-data-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);

  .loading-content {
    text-align: center;
    color: #6c757d;
  }
}

.error-overlay {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 2rem;

  ::ng-deep {
    .p-message {
      max-width: 400px;
    }
  }
}

.no-data-overlay {
  background-color: rgba(255, 255, 255, 0.95);

  .no-data-content {
    max-width: 400px;
    padding: 2rem;

    i {
      display: block;
    }

    h5 {
      margin-bottom: 0.5rem;
    }

    p {
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }
  }
}

// Customizações PrimeNG
::ng-deep {
  .p-button {
    border-radius: 6px;
    font-weight: 500;

    &.p-button-outlined {
      background-color: transparent;
    }
  }

  .p-progressspinner-circle {
    stroke: #007bff;
  }

  .filtros-sidebar {
    width: 90vw !important;
    max-width: 400px !important;

    .p-sidebar-content {
      padding: 0;
    }
  }

  .p-sidebar-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
  }
}

// Responsividade
@media (max-width: 768px) {
  .mapa-header {
    .flex {
      flex-wrap: wrap;
      gap: 0.5rem;

      h4 {
        font-size: 1.1rem;
      }

      .text-sm {
        font-size: 0.75rem;
      }
    }
  }

  .no-data-overlay {
    .no-data-content {
      padding: 1rem;
      max-width: 300px;

      i {
        font-size: 3rem !important;
      }

      h5 {
        font-size: 1rem;
      }

      p {
        font-size: 0.875rem;
      }
    }
  }
}

@media (max-width: 576px) {
  .mapa-header {
    .flex {
      flex-direction: column;
      align-items: flex-start;

      &:last-child {
        align-items: flex-end;
        width: 100%;
        margin-top: 0.5rem;
      }
    }
  }
}

// Animações
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-content,
.no-data-content {
  animation: fadeIn 0.3s ease-in-out;
}

// Estados de hover para botões
.mapa-header {
  .p-button {
    transition: all 0.2s ease-in-out;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
