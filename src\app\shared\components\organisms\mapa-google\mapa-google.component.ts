import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  Input,
  OnDestroy,
  OnInit,
  signal,
  viewChild,
  viewChildren
} from '@angular/core';
import { Subject } from 'rxjs';

// Google Maps
import { GoogleMapsModule, MapInfoWindow, MapMarker } from '@angular/google-maps';

// Models
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { EquipamentoResponse } from '@core/models/equipamento.model';

@Component({
  selector: 'app-mapa-google',
  standalone: true,
  imports: [
    CommonModule,
    GoogleMapsModule
  ],
  templateUrl: './mapa-google.component.html',
  styleUrl: './mapa-google.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapaGoogleComponent implements OnInit, OnDestroy, AfterViewInit {
  private destroy$ = new Subject<void>();
  private sanitizer = inject(DomSanitizer);
  private markersRef = viewChildren<MapMarker>(MapMarker);
  private infoWindowRef = viewChild<MapInfoWindow>(MapInfoWindow);

  @Input() set equipamentos(value: EquipamentoResponse[]) {
    this._equipamentos.set(value || []);
  }

  private _equipamentos = signal<EquipamentoResponse[]>([]);

  get equipamentosSignal() {
    return this._equipamentos.asReadonly();
  }

  // Configurações do mapa
  center = signal<google.maps.LatLngLiteral>({ lat: -15.7942, lng: -47.8822 });
  zoom = signal<number>(10);

  // Opções do mapa
  options = signal<google.maps.MapOptions>({
    mapTypeId: 'roadmap',
    zoomControl: true,
    scrollwheel: true,
    disableDoubleClickZoom: false,
    maxZoom: 20,
    minZoom: 10,
    styles: [
      {
        featureType: 'poi',
        elementType: 'labels',
        stylers: [{ visibility: 'off' }]
      }
    ]
  });

  // InfoWindow
  infoWindowVisible = signal<boolean>(false);
  infoWindowContent = signal<SafeHtml>('');
  infoWindowPosition = signal<google.maps.LatLngLiteral>({ lat: 0, lng: 0 });

  // Marcadores computados
  markers = computed(() => {
    return this._equipamentos().map(equipamento => {
      const lat = parseFloat(equipamento.latitude);
      const lng = parseFloat(equipamento.longitude);

      if (isNaN(lat) || isNaN(lng)) {
        return null;
      }

      const iconUrl = this.getIconForEquipmentType(equipamento);
      // const iconUrl = this.createMarkerSVG(equipamento.tempoDias.toString());
      // const svgUrl = 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(iconUrl);
      return {
        position: { lat, lng },
        title: `${equipamento.nred} - ${equipamento.endereco}`,
        options: {
          icon: {
            url: iconUrl,
            scaledSize: new google.maps.Size(32, 32), // Proporção baseada no viewBox 100x73
            // size: new google.maps.Size(40, 29)
          },
        },
        equipamento
      };
    }).filter(marker => marker !== null);
  });

  ngOnInit(): void {
    // Inicialização se necessária
  }

  ngAfterViewInit(): void {
    // Configurações após a view ser inicializada
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Mapeia o tipo de equipamento para o ícone correspondente
   */
  private getIconForEquipmentType(equipamento: EquipamentoResponse): string {
    const iconMap: { [key: string]: string } = {
      'CP': 'cp_',
      'CR': 'cr_',
      'LX': 'lx_',
      'TR': 'tr_',
      'CA': 'ca_',
      'CO': 'co_'
    };

    const iconExtension: { [key: string]: string } = {
      'CP': '.svg',
      'CR': '.png',
      'LX': '.png',
      'TR': '.png',
      'CA': '.png',
      'CO': '.png'
    }

    const iconFileName = iconMap[equipamento.tipoEquipamento];
    return `assets/images/${iconFileName}` + this.getMarkerColor(equipamento) + (iconExtension[equipamento.tipoEquipamento] || '.png');
  }

  /**
   * Determina a cor do marcador baseado na data de previsão de coleta
   */
  private getMarkerColor(equipamento: EquipamentoResponse): string {

    try {
      // Converte a data de previsão (formato DD/MM/YYYY)
      const [dia, mes, ano] = equipamento.dataPrevisaoColeta.split('/');
      const dataPrevisao = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));

      // Adiciona os dias limite
      const dataLimite = new Date(dataPrevisao);

      const hoje = new Date();
      hoje.setHours(0, 0, 0, 0); // Remove horas para comparação apenas de datas
      dataLimite.setHours(0, 0, 0, 0);

      if (dataLimite > hoje) {
        return 'verde'; // Verde - dentro do prazo
      } else if (dataLimite.getTime() === hoje.getTime()) {
        return 'amarelo'; // Amarelo - vence hoje
      } else {
        return 'vermelho'; // Vermelho - vencido
      }
    } catch (error) {
      console.warn('Erro ao calcular cor do marcador:', error);
      return '#6c757d'; // Cinza para erro
    }
  }

  /**
   * Manipula o clique no marcador
   */
  onMarkerClick(windowIndex: number, marker: any): void {
    console.log(marker)
    const equipamento = marker.equipamento;

    // Cria o conteúdo do InfoWindow
    const infoContent = this.sanitizer.bypassSecurityTrustHtml(this.createInfoWindowContent(equipamento));

    // Configura e mostra o InfoWindow
    this.infoWindowPosition.set(marker.position);
    this.infoWindowVisible.set(true);
    this.markersRef().forEach((marker, index) => {
      if (index === windowIndex) {
        this.infoWindowContent.set(infoContent);
        this.infoWindowRef()?.open(marker)
      }
    })

    console.log('Marcador clicado:', equipamento);
  }

  /**
   * Fecha o InfoWindow
   */
  closeInfoWindow(): void {
    this.infoWindowVisible.set(false);
  }

  /**
   * Cria o conteúdo do InfoWindow
   */
  private createInfoWindowContent(equipamento: EquipamentoResponse): string {
    return `
    <div class="info-window" style="position: relative; padding-top: 40px;">
      <div class="header-info">
        <span>ID: ${equipamento.id}</span>
        <img src="${this.getIconForEquipmentType(equipamento)}" alt="Ícone" />
      </div>

      <h6>${equipamento.nred}</h6>
      <p><strong>Tipo:</strong> ${equipamento.tipoEquipamento}</p>
      <p><strong>Endereço:</strong> ${equipamento.endereco}</p>
      <p><strong>Bairro:</strong> ${equipamento.bairro}</p>
      <p><strong>Cliente:</strong> ${equipamento.nome ?? ''}</p>
      <p><strong>Situação:</strong> ${equipamento.situacao === 'A' ? 'Ativo' : 'Inativo'}</p>
      <p><strong>Última Movimentação:</strong> ${equipamento.dataUltMovimento}</p>
      <p><strong>Previsão de Coleta:</strong> ${equipamento.dataPrevisaoColeta}</p>
      <p><strong>Tempo (dias):</strong> ${equipamento.tempoDias}</p>
    </div>
    `;
  }

  /**
   * Centraliza o mapa em uma posição específica
   */
  centerMap(lat: number, lng: number, zoom?: number): void {
    this.center.set({ lat, lng });
    if (zoom) {
      this.zoom.set(zoom);
    }
  }

  /**
   * Ajusta o zoom para mostrar todos os equipamentos
   */
  fitBounds(): void {
    const markers = this.markers();
    if (markers.length === 0) return;

    const bounds = new google.maps.LatLngBounds();
    markers.forEach(marker => {
      if (marker) {
        bounds.extend(marker.position);
      }
    });

    // Aqui você precisaria acessar a instância do mapa para usar fitBounds
    // Isso pode ser feito através de ViewChild ou eventos do GoogleMapsModule
  }

  /**
   * Atualiza os marcadores (chamado quando os equipamentos mudam)
   */
  updateMarkers(): void {
    // Os marcadores são atualizados automaticamente através do computed signal
    // Este método pode ser usado para lógica adicional se necessário
  }
}
