import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class BreakpointService {

  private readonly breakpointObserver = inject(BreakpointObserver);

  private readonly layoutChanges = toSignal(
    this.breakpointObserver
      .observe(Object.values(Breakpoints))
      .pipe(map(({ breakpoints }) => breakpoints)),
  );

  public readonly isWeb = computed(
    () => this.layoutChanges()?.[Breakpoints.Web] ?? false,
  );

  public readonly isWebPortrait = computed(
    () => this.layoutChanges()?.[Breakpoints.WebPortrait] ?? false,
  );

  public readonly isXSmall = computed(
    () => this.layoutChanges()?.[Breakpoints.XSmall] ?? false,
  );

}
