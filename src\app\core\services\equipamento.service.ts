import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { EquipamentoFiltro, EquipamentoResponse } from '../models/equipamento.model';

@Injectable({
  providedIn: 'root'
})
export class EquipamentoService {
  private readonly baseUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Busca todos os equipamentos com base nos filtros aplicados
   */
  findAll(filtros: EquipamentoFiltro): Observable<EquipamentoResponse[]> {
    let params = new HttpParams();

    if (filtros.codFil) {
      params = params.set('codFil', filtros.codFil.toString());
    }

    if (filtros.tipoEquip && filtros.tipoEquip.length > 0) {
      filtros.tipoEquip.forEach(tipo => {
        params = params.append('tipoEquip', tipo);
      });
    }

    if (filtros.nred && filtros.nred.length > 0) {
      filtros.nred.forEach(nred => {
        params = params.append('nred', nred);
      });
    }

    if (filtros.bairro && filtros.bairro.length > 0) {
      filtros.bairro.forEach(bairro => {
        params = params.append('bairro', bairro);
      });
    }

    if (filtros.nredFat && filtros.nredFat.length > 0) {
      filtros.nredFat.forEach(nredFat => {
        params = params.append('nredFat', nredFat);
      });
    }

    if (filtros.solicitante && filtros.solicitante.length > 0) {
      filtros.solicitante.forEach(solicitante => {
        params = params.append('solicitante', solicitante);
      });
    }

    return this.http.get<EquipamentoResponse[]>(`${this.baseUrl}/equipamentos`, { params });
  }

  /**
   * Busca lista de clientes para autocomplete
   */
  findAllClients(search?: string): Observable<string[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('client', search);
    }
    return this.http.get<string[]>(`${this.baseUrl}/equipamentos/clientes`, { params });
  }

  /**
   * Busca lista de serviços para autocomplete
   */
  findAllServices(search?: string): Observable<string[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('service', search);
    }
    return this.http.get<string[]>(`${this.baseUrl}/equipamentos/servicos`, { params });
  }

  /**
   * Busca lista de solicitantes para autocomplete
   */
  findAllRequesters(search?: string): Observable<string[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('requester', search);
    }
    return this.http.get<string[]>(`${this.baseUrl}/equipamentos/solicitantes`, { params });
  }

  /**
   * Busca lista de bairros para autocomplete
   */
  findAllPlaces(search?: string): Observable<string[]> {
    let params = new HttpParams();
    if (search) {
      params = params.set('place', search);
    }
    return this.http.get<string[]>(`${this.baseUrl}/equipamentos/bairros`, { params });
  }

  /**
   * Busca informações da filial
   */
  getFilialInfo(): Observable<FilialInfo> {
    return this.http.get<FilialInfo>(`${this.baseUrl}/filiais`);
  }

  findEquipmentWithoutGeolocalization(): Observable<EquipamentoResponse[]> {
    return this.http.get<EquipamentoResponse[]>(`${this.baseUrl}/equipamentos?semPosicao=true`);
  }
}

export interface FilialInfo {
  codigoFormatado: string;
  codigo: string;
  nome: string;
  endereco: string;
  bairro: string;
  cidade: string;
  uf: string;
}
