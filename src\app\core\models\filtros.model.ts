export interface FiltroOption {
  label: string;
  value: string;
  icon?: string;
}

export interface MapaFiltros {
  empresa?: string;
  usuario?: string;
  filial?: number;
  tiposEquipamento: string[];
  servicos: string[];
  clientes: string[];
  bairros: string[];
  solicitantes: string[];
  dataInicio?: Date;
  dataFim?: Date;
}

export interface AutocompleteResponse {
  items: string[];
  total: number;
}

export const FILTROS_INICIAIS: MapaFiltros = {
  tiposEquipamento: [],
  servicos: [],
  clientes: [],
  bairros: [],
  solicitantes: []
};
