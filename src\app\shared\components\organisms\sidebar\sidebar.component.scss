.custom-sidebar-backdrop {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.35);
  z-index: 1000;
}

.custom-sidebar {
  position: fixed;
  top: 0;
  right: -100%;
  width: 400px;
  height: 100%;
  background: white;
  box-shadow: -2px 0 6px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: right 0.3s ease-in-out;

  display: flex;
  flex-direction: column;

  &.visible {
    right: 0;
  }

  .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;

    span {
      color: black;
    }

    .close-btn {
      color: black;
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
    }
  }

  .sidebar-content {
    padding: 1rem;
    overflow-y: auto;
    flex: 1;
  }
}
