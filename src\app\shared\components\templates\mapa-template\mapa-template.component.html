<div class="mapa-container h-screen w-full">
  <!-- Header com controles -->
  <div class="mapa-header">
    <div class="flex align-items-center justify-content-between p-3 bg-white border-bottom-1 surface-border">
      <div class="flex align-items-center gap-3">
        <h4 class="m-0 text-primary">
          <i class="pi pi-map mr-2"></i>
          Mapa de Equipamentos
        </h4>
        
        @if (equipamentos().length > 0) {
          <span class="text-sm text-muted">
            {{ equipamentos().length }} equipamento(s) encontrado(s)
          </span>
        }
      </div>

      <div class="flex align-items-center gap-2">
        <!-- Botão para centralizar em Brasília -->
        <p-button
          icon="pi pi-home"
          [outlined]="true"
          size="small"
          pTooltip="Centralizar em Brasília"
          tooltipPosition="bottom"
          (onClick)="centerMapBrasilia()">
        </p-button>

        <!-- Botão para ajustar zoom aos equipamentos -->
        <p-button
          icon="pi pi-search-plus"
          [outlined]="true"
          size="small"
          pTooltip="Ajustar zoom aos equipamentos"
          tooltipPosition="bottom"
          [disabled]="equipamentos().length === 0"
          (onClick)="fitAllEquipments()">
        </p-button>

        <!-- Botão para recarregar -->
        <p-button
          icon="pi pi-refresh"
          [outlined]="true"
          size="small"
          pTooltip="Recarregar dados"
          tooltipPosition="bottom"
          [loading]="loading()"
          (onClick)="reloadData()">
        </p-button>

        <!-- Botão para toggle do painel de filtros -->
        <p-button
          [icon]="sidebarVisible() ? 'pi pi-times' : 'pi pi-filter'"
          [label]="isMobile() ? '' : 'Filtros'"
          size="small"
          [severity]="sidebarVisible() ? 'secondary' : 'primary'"
          (onClick)="toggleFiltros()">
        </p-button>
      </div>
    </div>
  </div>

  <!-- Conteúdo principal -->
  <div class="mapa-content flex h-full">
    
    <!-- Painel de filtros (desktop) -->
    @if (!isMobile()) {
      <div class="filtros-panel" [class.visible]="sidebarVisible()">
        <app-painel-filtros></app-painel-filtros>
      </div>
    }

    <!-- Área do mapa -->
    <div class="mapa-area flex-1 relative">
      
      <!-- Loading overlay -->
      @if (loading()) {
        <div class="loading-overlay">
          <div class="loading-content">
            <p-progressSpinner 
              [style]="{ width: '50px', height: '50px' }"
              strokeWidth="4">
            </p-progressSpinner>
            <p class="mt-3 text-center">Carregando equipamentos...</p>
          </div>
        </div>
      }

      <!-- Error message -->
      @if (error()) {
        <div class="error-overlay">
          <p-message 
            severity="error" 
            [text]="error()!"
            [closable]="true"
            (onClose)="error.set(null)">
          </p-message>
        </div>
      }

      <!-- Componente do mapa -->
      <app-mapa-google 
        [equipamentos]="equipamentos()"
        class="w-full h-full">
      </app-mapa-google>

      <!-- Info panel (quando não há equipamentos) -->
      @if (!loading() && equipamentos().length === 0 && !error()) {
        <div class="no-data-overlay">
          <div class="no-data-content text-center">
            <i class="pi pi-info-circle text-6xl text-muted mb-3"></i>
            <h5 class="text-muted">Nenhum equipamento encontrado</h5>
            <p class="text-sm text-muted">
              Ajuste os filtros para encontrar equipamentos ou verifique se há dados disponíveis.
            </p>
            <p-button
              label="Limpar Filtros"
              icon="pi pi-filter-slash"
              [outlined]="true"
              size="small"
              (onClick)="clearFilters()">
            </p-button>
          </div>
        </div>
      }
    </div>
  </div>

  <!-- Sidebar para filtros (mobile) -->
  @if (isMobile()) {
    <p-sidebar
      [(visible)]="sidebarVisible"
      position="right"
      [modal]="true"
      [dismissible]="true"
      styleClass="filtros-sidebar">
      
      <ng-template pTemplate="header">
        <div class="flex align-items-center gap-2">
          <i class="pi pi-filter"></i>
          <span class="font-semibold">Filtros</span>
        </div>
      </ng-template>

      <app-painel-filtros></app-painel-filtros>
    </p-sidebar>
  }
</div>
